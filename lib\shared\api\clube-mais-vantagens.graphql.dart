import 'dart:async';

import 'package:cliente_minha_unimed/models/club-mais-vantagens.model.dart';
import 'package:cliente_minha_unimed/shared/api/graphql.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:graphql/client.dart';
import 'package:http_client/http_client.dart';

class ClubeMaisVantagensApi extends GraphQlApi {
  ClubeMaisVantagensApi(UnimedHttpClient httpClient) : super(httpClient);

  final UnimedLogger logger = UnimedLogger(className: 'ClubeMaisVantagensApi');


    Future<ClubMaisVantagensModel> verificaCadastroClubMaisVantagens({required String token}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query;
      query = '''
      query MoreAdvantagesVerifyData {
          moreAdvantagesVerify(
              token: "$token"
          ) {
              userRegistered
              link
              message
              ok
          }
      }
    ''';

      logger.i('moreAdvantagesVerify query : $query');

      final options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final result = await client.query(options).timeout(const Duration(seconds: 120));

      if (result.hasException) {
        logger.e('moreAdvantagesVerify exception : ${result.exception}');
        throw UnimedException('Não foi possível no momento.');
      } else {
        final data = result.data!['moreAdvantagesVerify'];
        logger.d('moreAdvantagesVerify success');

        return ClubMaisVantagensModel.fromJson(data);
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException moreAdvantagesVerify -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('moreAdvantagesVerify ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('moreAdvantagesVerify exception : $ex');
      throw UnimedException('Não foi possível no momento.');
    }
  }

  Future<ClubMaisVantagensModel> cadastrarClubeMaisVantagens({required String token}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
          query RegisterMoreAdvantagesData {
            moreAdvantagesRegister(
                token: "$token"
              ) {
              message
              ok
            } 
          }
      ''';

      logger.i('moreAdvantagesRegister query : $query');

      final options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final result = await client.query(options).timeout(const Duration(seconds: 120));

      if (result.hasException) {
        logger.e('moreAdvantagesRegister exception : ${result.exception}');
        throw GraphQlException(result.exception?.graphqlErrors.first.message ?? MessageException.GENERAL);
      } else {
        final data = result.data!['moreAdvantagesRegister'];
        logger.d('moreAdvantagesRegister success');

        final ClubMaisVantagensModel clubMaisVantagensModel = ClubMaisVantagensModel.fromJson(data);

        return clubMaisVantagensModel;
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException moreAdvantagesRegister -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('moreAdvantagesRegister ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('moreAdvantagesRegister exception : $ex');
      throw UnimedException('Não foi possível no momento.');
    }
  }
}