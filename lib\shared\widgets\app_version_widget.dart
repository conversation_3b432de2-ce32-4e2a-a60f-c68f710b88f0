import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppVersionWidget extends StatefulWidget {
  final Color? textColor;
  final double? fontSize;
  final FontWeight? fontWeight;
  final EdgeInsets? padding;

  const AppVersionWidget({
    Key? key,
    this.textColor,
    this.fontSize,
    this.fontWeight,
    this.padding,
  }) : super(key: key);

  @override
  _AppVersionWidgetState createState() => _AppVersionWidgetState();
}

class _AppVersionWidgetState extends State<AppVersionWidget> {
  String _version = '';
  String _buildNumber = '';

  @override
  void initState() {
    super.initState();
    _getAppVersion();
  }

  /// Obtém a versão e build number do aplicativo
  Future<void> _getAppVersion() async {
    try {
      final PackageInfo packageInfo = await PackageInfo.fromPlatform();
      setState(() {
        _version = packageInfo.version;
        _buildNumber = packageInfo.buildNumber;
      });
    } catch (e) {
      setState(() {
        _version = '';
        _buildNumber = '';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: widget.padding ?? const EdgeInsets.all(16.0),
      child: Center(
        child: Text(
          _version.isNotEmpty
              ? 'Versão $_version'
              : ' ',
          style: TextStyle(
            color: widget.textColor ?? Colors.grey[600],
            fontSize: widget.fontSize ?? 12.0,
            fontWeight: widget.fontWeight ?? FontWeight.normal,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}


class BottomAppVersionWidget extends StatelessWidget {
  final Color? textColor;
  final double? fontSize;
  final FontWeight? fontWeight;
  final double? bottom;

  const BottomAppVersionWidget({
    Key? key,
    this.textColor,
    this.fontSize,
    this.fontWeight,
    this.bottom,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: 0,
      right: 0,
      bottom: bottom ?? 16.0,
      child: AppVersionWidget(
        textColor: textColor,
        fontSize: fontSize,
        fontWeight: fontWeight,
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
      ),
    );
  }
}
