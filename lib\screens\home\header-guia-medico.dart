import 'package:auto_size_text/auto_size_text.dart';
import 'package:cliente_minha_unimed/bloc/favorites/favorites_bloc.dart';
import 'package:cliente_minha_unimed/bloc/favorites/favorites_state.dart';
import 'package:cliente_minha_unimed/bloc/general_config/general_config_bloc.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/medical_guide_bloc.dart';
import 'package:cliente_minha_unimed/bloc/medical-guide/medical_guide_event.dart';
import 'package:cliente_minha_unimed/bloc/notificacao/notificacao_bloc.dart';
import 'package:cliente_minha_unimed/bloc/osb/user_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_state.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_cubit.dart';
import 'package:cliente_minha_unimed/bloc/sensitive-data/sensitive_data_state.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/medical-guide/medical-guide-search.model.dart';
import 'package:cliente_minha_unimed/models/perfil.model.dart';
import 'package:cliente_minha_unimed/screens/home/<USER>';
import 'package:cliente_minha_unimed/screens/home/<USER>';
import 'package:cliente_minha_unimed/screens/medical-guide/main.dart';
import 'package:cliente_minha_unimed/screens/medical-guide/widgets/advanced-search.dart';
import 'package:cliente_minha_unimed/screens/notificacao/main.dart';
import 'package:cliente_minha_unimed/shared/base_state.dart';
import 'package:cliente_minha_unimed/shared/flavor-config.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/screen_transitions/scale.transition.dart';
import 'package:cliente_minha_unimed/shared/screen_transitions/size.transition.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/services/version.service.dart';
import 'package:cliente_minha_unimed/shared/utils/router-observer.dart';
import 'package:cliente_minha_unimed/shared/utils/sensitive-data-button.dart';
import 'package:cliente_minha_unimed/shared/utils/string_utils.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert.dart';
import 'package:cliente_minha_unimed/shared/widgets/snack.dart';
// import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:teleconsulta_unimed/shared/perfilapps.constants.dart';
import 'package:teleconsulta_unimed/shared/services/twilio_service.dart';

const double FONT_SIZE_SMALL = 14;
const double FONT_SIZE_BIG = 20;
const double LEFT_PADDING = 13;
const double HEIGHT = 225;
const String url = "https://www.unimedfortaleza.com.br/politica-de-privacidade";

class HeaderGuiaMedico extends StatefulWidget {
  HeaderGuiaMedico({Key? key}) : super(key: key);

  _HeaderGuiaMedicoState createState() => _HeaderGuiaMedicoState();
}

class _HeaderGuiaMedicoState extends BaseState<HeaderGuiaMedico>
    with RouteAware {
  bool enableButtonGuiaMedico = false;
  bool isLoggingOut = false;
  bool testTwilioEnable = true;
  int? _favoritesQuantity;
  // final _myFirebaseAnalytics = Locator.instance.get<FirebaseAnalytics>();
  final TextEditingController _tecTexto = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  static const String baseTranslate = 'medicalGuide.header';

  @override
  void initState() {
    super.initState();

    _tecTexto.addListener(_listenerTecTexto);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute<dynamic>);
  }

  @override
  void dispose() {
    super.dispose();
    routeObserver.unsubscribe(this);
    _tecTexto.removeListener(_listenerTecTexto);
    _focusNode.dispose();
  }

  void didPopNext() {
    // Fecha o teclado e remove foco do campo quando volta de outra tela
    if (mounted) {
      _focusNode.unfocus();
      FocusScope.of(context).unfocus();
    }
  }

  void _listenerTecTexto() {
    setState(() {
      enableButtonGuiaMedico = _tecTexto.text.trim().length >= 3;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: Stack(
          children: <Widget>[
            _ellipseBottom(context),
            _ellipseTop(context),
            _upperLayer(context),
          ],
        ),
      ),
    );
  }

  Widget _upperLayer(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification scrollInfo) {
        // Fecha o teclado apenas se estiver aberto quando o usuário faz scroll
        final currentFocus = FocusScope.of(context);
        if (currentFocus.hasFocus) {
          currentFocus.unfocus();
        }
        return false;
      },
      child: Container(
        height: HEIGHT,
        child: Column(
          children: [
            _logoRow(),
            Expanded(
              child: BlocBuilder<SensitiveDataCubit, SensitiveDataState>(
                  builder: (context, stateSensitiveData) {
                return BlocBuilder<PerfilBloc, PerfilState>(
                  builder: (context, state) {
                    final perfil = BlocProvider.of<PerfilBloc>(context).perfil;

                    String message = translate('$baseTranslate.initialMessage');

                    message = translate(
                      '$baseTranslate.hello',
                      params: {
                        'name': stateSensitiveData.isSensitiveDataVisible
                            ? StringUtils.formahideDatatMessage(
                                perfil.firstAndLastName)
                            : perfil.firstAndLastName
                      },
                    );

                    return Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Text(
                          message,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: FONT_SIZE_SMALL,
                          ),
                        ),
                      ],
                    );
                  },
                );
              }),
            ),
            _medicalGuideLabel(),
            _advancedSearchButton(),
            const SizedBox(height: 8.0),
          ],
        ),
      ),
    );
  }

  _logoRow() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: LEFT_PADDING),
      child: Row(
        children: [
          GestureDetector(
            onDoubleTap: () {
              _alertVersion(context);
            },
            onLongPress: () {
              if (!FlavorConfig.isProduction()) {
                _openModal();
              }
            },
            child: Hero(
              tag: 'logo-unimed',
              child: Image.asset(
                'assets/images/logo-unimed.png',
                width: 100,
              ),
            ),
          ),
          Expanded(child: SizedBox()),
          _topBarButtons(),
        ],
      ),
    );
  }

  _medicalGuideLabel() {
    return Row(
      children: [
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(left: LEFT_PADDING, top: 4.0),
            child: AutoSizeText(
              translate('common.medicalGuide'),
              style: TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: FONT_SIZE_BIG,
              ),
              maxLines: 1,
              minFontSize: FONT_SIZE_SMALL,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ),
      ],
    );
  }

  Widget _favoriteButton(int? favoritesQuantity) {
    if (favoritesQuantity == null || favoritesQuantity == 0) return Container();

    return InkWell(
      key: Key('btnStars'),
      onTap: () {
        BlocProvider.of<MedicalGuideBloc>(context).add(OpenFavorites(
          perfil: BlocProvider.of<PerfilBloc>(context).perfil,
          prestadores: BlocProvider.of<FavoriteBloc>(context).favorites,
        ));

        Navigator.push(
          context,
          ScaleRoute(
            page: MedicalGuideScreen(
              isFavoriteScreen: true,
            ),
          ),
        );
      },
      child: Container(
        height: 50,
        margin: const EdgeInsets.only(right: 8.0),
        child: Icon(
          Icons.star,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _topBarButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: <Widget>[
        BlocBuilder<FavoriteBloc, FavoriteState>(
          builder: (context, state) {
            if (state is LoadedFavoriteState) {
              if (state.favorites != null && state.favorites!.length > 0) {
                _favoritesQuantity = state.favorites!.length;
              }
            }

            return _favoriteButton(_favoritesQuantity);
          },
        ),

        StreamBuilder(
          stream: BlocProvider.of<NotificacaoBloc>(context).streamNotifications,
          builder: (context, snapshot) {
            final count =
                (snapshot.data?.where((element) => element?.readAt == null) ??
                        [])
                    .length;
            return InkWell(
              onTap: () {
                Navigator.push(context, SizeRoute(page: NotificacaoScreen()));
              },
              child: count == 0
                  ? Icon(
                      Icons.notifications_none,
                      color: Colors.white,
                    )
                  : Badge.count(
                      count: count,
                      child: Icon(
                        Icons.notifications_none,
                        color: Colors.white,
                      ),
                    ),
            );
          },
        ),
        SensitibeDataButton(),
        _profileHomeButton(),
        ConfigsHomeButton(),
        // _selectPopup(context),
      ],
    );
  }

  Widget _advancedSearchButton() {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: LEFT_PADDING),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: <Widget>[
          _textField(context, _tecTexto),
          Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: () => Navigator.push(
                context,
                ScaleRoute(
                  page: MedicalGuideAdvancedSearch(),
                ),
              ),
              child: Ink(
                decoration: BoxDecoration(
                  color: UnimedColors.green,
                  border: Border.all(color: Colors.white),
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(8),
                    topLeft: Radius.circular(8),
                    bottomLeft: Radius.circular(8),
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                      vertical: 5.0, horizontal: 10.0),
                  child: Text(
                    translate('$baseTranslate.advancedSearch'),
                    style: TextStyle(color: Colors.white),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _textField(BuildContext context, TextEditingController controller) {
    return Row(
      children: <Widget>[
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: 4.0, bottom: 4.0),
            child: Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10.0),
                  bottomLeft: Radius.circular(10.0),
                ),
              ),
              child: TextFormField(
                enableInteractiveSelection: false,
                autofocus: false,
                controller: controller,
                focusNode: _focusNode,
                textInputAction: TextInputAction.search,
                style: TextStyle(color: unimedGreen),
                onFieldSubmitted: (term) {
                  if (term.trim().isNotEmpty) {
                    _search(context);
                  }
                },
                maxLength: 27,
                maxLines: 1,
                decoration: InputDecoration(
                  counter: Offstage(),
                  hintText: translate('$baseTranslate.inputHint'),
                  hintMaxLines: null,
                  contentPadding: EdgeInsets.only(
                    top: 12,
                    left: 10,
                    right: 10,
                    bottom: 6,
                  ),
                  border: InputBorder.none,
                ),
              ),
            ),
          ),
        ),
        Align(
          alignment: Alignment.centerRight,
          child: _guideSearchButton(),
        ),
      ],
    );
  }

  _guideSearchButton() {
    return Container(
      key: Key('btnGuiaMedico'),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topRight: Radius.circular(10.0),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 2, right: 2, bottom: 2),
        child: Material(
          child: InkWell(
            splashColor: unimedGreen.shade400, // inkwell color
            child: Ink(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(10.0),
                  topRight: Radius.circular(10.0),
                  bottomLeft: Radius.circular(10.0),
                ),
                //border: Border.all(color: Colors.white, width: 3),
                color: unimedGreen, // button color
              ),
              width: 44,
              height: 44,
              child: Icon(
                Icons.send,
                color: Colors.white,
              ),
            ),
            onTap: () {
              _search(context);
            },
          ),
        ),
      ),
    );
  }

  void _search(BuildContext context) {
    final value = _tecTexto.text.trim();

    if (value.isEmpty || value.length < 3) {
      SchedulerBinding.instance.addPostFrameCallback((_) {
        ScaffoldMessenger.of(context).showSnackBar(
          Snack.warning(
            translate('$baseTranslate.emptySearch'),
            duration: Duration(seconds: 1),
          ),
        );
      });
    } else {
      // this._myFirebaseAnalytics.logSearch(searchTerm: value);
      SchedulerBinding.instance.addPostFrameCallback((_) {
        Navigator.push(
          context,
          ScaleRoute(
            page: MedicalGuideScreen(
              searchParams: SearchMedicalGuideModel(
                provider: value,
              ),
              isFavoriteScreen: false,
            ),
          ),
        );
      });
    }
  }

  Widget _ellipseBottom(BuildContext context) {
    return Container(
      width: double.infinity,
      height: HEIGHT,
      child: CustomPaint(
        painter: _EllipseBottom(context),
      ),
    );
  }

  Widget _ellipseTop(BuildContext context) {
    return Container(
      width: double.infinity,
      height: HEIGHT,
      child: CustomPaint(
        painter: _EllipseTop(context),
      ),
    );
  }

  _alertVersion(BuildContext context) async {
    VersionInfo info = await Locator.instance.get<VersionService>().getInfo();

    Alert.open(
      context,
      title: translate('$baseTranslate.version'),
      text: '${info.version} (${info.buildNumber})',
    );
  }

  _openModal() {
    final PerfilAppsConstants _perfilAppsConstants = PerfilAppsConstants(
      user: FlavorConfig.instance!.values.profilePermissions.user,
      password: FlavorConfig.instance!.values.profilePermissions.password,
      url: FlavorConfig.instance!.values.profilePermissions.url as String,
    );

    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Wrap(
          children: [
            GestureDetector(
              child: ListTile(
                title: Text('Twilio'),
              ),
              onTap: () async {
                if (testTwilioEnable) {
                  setState(() {
                    testTwilioEnable = false;
                  });
                  await TwilioService.testRoom(context, "ugabuga123",
                      "Cliente Teste", _perfilAppsConstants, []);

                  setState(() {
                    testTwilioEnable = true;
                  });
                }
              },
            ),
            GestureDetector(
              child: ListTile(
                title: Text('Zoom'),
              ),
              onTap: () async {
                if (testTwilioEnable) {
                  setState(() {
                    testTwilioEnable = false;
                  });
                  // await ZoomService.testRoom(
                  //   context,
                  //   "ugabuga123",
                  //   "Cliente Teste",
                  //   _perfilAppsConstants,
                  // );

                  setState(() {
                    testTwilioEnable = true;
                  });
                }
              },
            ),
          ],
        );
      },
    );
  }

  Widget _profileHomeButton() {
    return Padding(
      padding: const EdgeInsets.only(left: 12.0),
      child: InkWell(
        onTap: () {
          _openProfilePicker(context);
        },
        child: Icon(
          Icons.person_outline,
          color: Colors.white,
        ),
      ),
    );
  }

  Future<void> _openProfilePicker(
    BuildContext context, {
    Function(Perfil)? onUpdate,
  }) async {
    AnalyticsService().logButtonClick({'type': 'button', 'value': 'perfil'});

    return await PerfilMenu.open(
      context,
      BlocProvider.of<UserBloc>(context).user,
      onUpdate: onUpdate,
      showSettings: BlocProvider.of<GeneralConfigBloc>(context)
              .generalConfigModel
              ?.buttons
              .profilePreviewPermission ??
          false,
    );
  }
}

class _EllipseBottom extends CustomPainter {
  final BuildContext context;

  _EllipseBottom(this.context);

  @override
  void paint(Canvas canvas, Size size) {
    final width = MediaQuery.of(context).size.width;

    Paint paint = new Paint()
      ..style = PaintingStyle.fill
      ..isAntiAlias = true
      ..color = unimedOrange;

    Rect rect = Rect.fromPoints(
      Offset(-50, -90),
      Offset(width * 1.3, HEIGHT * 1.1),
    );

    canvas.drawOval(rect, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}

class _EllipseTop extends CustomPainter {
  final BuildContext context;

  _EllipseTop(this.context);

  @override
  void paint(Canvas canvas, Size size) {
    final width = MediaQuery.of(context).size.width;

    Paint paint = new Paint()
      ..style = PaintingStyle.fill
      ..isAntiAlias = true
      ..color = unimedOrange.shade400;

    Rect rect = Rect.fromPoints(
      Offset(-60, -60),
      Offset(width * 0.85, HEIGHT * 0.93),
    );

    canvas.drawOval(rect, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}
