import 'dart:io';

import 'package:cliente_minha_unimed/bloc/offline/offline_bloc.dart';
import 'package:cliente_minha_unimed/bloc/offline/offline_event.dart';
import 'package:cliente_minha_unimed/bloc/offline/offline_state.dart';
import 'package:cliente_minha_unimed/bloc/osb/user_bloc.dart';
import 'package:cliente_minha_unimed/bloc/osb/user_event.dart';
import 'package:cliente_minha_unimed/bloc/osb/user_state.dart';
import 'package:cliente_minha_unimed/bloc/pending-issues/pending_issues_bloc.dart';
import 'package:cliente_minha_unimed/bloc/pending-issues/pending_issues_event.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_bloc.dart';
import 'package:cliente_minha_unimed/bloc/perfil/perfil_state.dart';
import 'package:cliente_minha_unimed/bloc/redefinir-senha/redefinir_senha_bloc.dart';
import 'package:cliente_minha_unimed/colors.dart';
import 'package:cliente_minha_unimed/models/perfil_offline.model.dart';
import 'package:cliente_minha_unimed/models/user.model.dart';
import 'package:cliente_minha_unimed/screens/cadastro/main.dart';
import 'package:cliente_minha_unimed/screens/home/<USER>';
import 'package:cliente_minha_unimed/screens/login/button-login.dart';
import 'package:cliente_minha_unimed/screens/login/button-signup.dart';
import 'package:cliente_minha_unimed/screens/login/button_login_by_totem.dart';
import 'package:cliente_minha_unimed/screens/redefinir-senha/main.dart';
import 'package:cliente_minha_unimed/screens/virtual-card/main.dart';
import 'package:cliente_minha_unimed/shared/api/auth.api.dart';
import 'package:cliente_minha_unimed/shared/base_state.dart';
import 'package:cliente_minha_unimed/shared/flavor-config.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/services/analytics.service.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:cliente_minha_unimed/shared/utils/router-observer.dart';
import 'package:cliente_minha_unimed/shared/utils/validators.dart';
import 'package:cliente_minha_unimed/shared/widgets/alert-cancelar-agendamento.dart';
import 'package:cliente_minha_unimed/shared/widgets/app_version_widget.dart';
import 'package:cliente_minha_unimed/shared/widgets/flavor_banner.dart';
import 'package:extended_masked_text/extended_masked_text.dart';
import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:header_login/header_login.dart';
import 'package:local_auth/local_auth.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';

class LoginPage extends StatefulWidget {
  final bool openBiometryOnInit;
  LoginPage({Key? key, this.openBiometryOnInit = true}) : super(key: key);

  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends BaseState<LoginPage>
    with WidgetsBindingObserver, RouteAware {
  final logger = UnimedLogger(className: 'LoginPage');
  static const baseImages = 'assets/images';

  final loginFormatter = MaskTextInputFormatter(
      mask: '###.###.###-##', filter: {"#": RegExp(r'[0-9]')});

  final localAuth = LocalAuthentication();

  final tecLogin = TextEditingController();
  final tecPassword = TextEditingController();
  final FocusNode _cpfFocus = FocusNode();
  final FocusNode _senhaFocus = FocusNode();
  final _mFirebaseAnalytics = Locator.instance.get<FirebaseAnalytics>();
  bool hidePassword = true;
  // bool openError = false;
  bool _canOpenBiometry = true;
  final _formKey = GlobalKey<FormState>();
  String password = '';
  bool _isLoading = false;

  static const String baseTranslate = 'login';
  bool testTwilioEnable = true;

  @override
  void initState() {
    super.initState();
    if (widget.openBiometryOnInit) {
      _openBiometry();
    }
    BlocProvider.of<PendingIssuesBloc>(context).add(SetToInitialStateEvent());
    WidgetsBinding.instance.addObserver(this);
    AnalyticsService().addLogScreenView(
      screenName: 'LoginPage',
      screenClass: 'LoginPage',
    );
  }

  //This validations was added to avoid Biometry open even with App in background
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.inactive) {
      _canOpenBiometry = false;
    } else if (state == AppLifecycleState.resumed) {
      _canOpenBiometry = true;
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    routeObserver.subscribe(this, ModalRoute.of(context) as PageRoute<dynamic>);
  }

  void didPopNext() {
    SchedulerBinding.instance.addPostFrameCallback((_) async {
      final credentials =
          await Locator.instance.get<AuthApi>().getCredentials();
      if (credentials != null &&
          credentials.cpf.isNotEmpty &&
          credentials.password!.isNotEmpty) {
        BlocProvider.of<OfflineBloc>(context).add(GetPerfilModelOfflineEvent(
            cpf: credentials.cpf.replaceAll(new RegExp(r'[^0-9]'), '')));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return FlavorBanner(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.white,
        body: AnnotatedRegion<SystemUiOverlayStyle>(
          value: SystemUiOverlayStyle.light,
          child: Stack(
            children: [
              SingleChildScrollView(
                scrollDirection: Axis.vertical,
                child: Column(
                  children: <Widget>[
                    GestureDetector(
                      child: HeaderLogin(
                        innerCircleColor: unimedOrange.shade400,
                        outerCircleColor: unimedOrange,
                      ),
                    ),
                    Center(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 30),
                        child: MultiBlocListener(
                          listeners: [
                            BlocListener<UserBloc, UserState>(
                              listener: (context, state) {
                                if (state is LoadingUserState ||
                                    state is LoadingLoginPermissionsState) {
                                  setState(() {
                                    _isLoading = true;
                                  });
                                } else if (state is ErrorUserState) {
                                  setState(() {
                                    _isLoading = false;
                                  });
                                } else if (state
                                    is LoadedLoginPermissionState) {
                                  setState(() {
                                    _isLoading = false;
                                  });
                                  Navigator.push(context,
                                      FadeRouteBuilder(page: CadastroScreen()));
                                }

                                if (state is LoadedUserState) {
                                  Future.delayed(Duration(seconds: 5), () {
                                    tecPassword.clear();
                                  });
                                }
                              },
                            ),
                            BlocListener<PerfilBloc, PerfilState>(
                              listener: (context, state) {
                                if (state is LoadingUserState ||
                                    state is LoadingLoginPermissionsState) {
                                  setState(() {
                                    _isLoading = true;
                                  });
                                } else if (state is ErrorPerfilState ||
                                    state is ErrorProfilesPermissionsState) {
                                  setState(() {
                                    _isLoading = false;
                                  });
                                }
                              },
                            ),
                          ],
                          child: _formLogin(),
                        ),
                      ),
                    ),
                    SizedBox(height: 60),
                  ],
                ),
              ),
              BottomAppVersionWidget(
                textColor: UnimedColors.blackText,
                fontSize: 13.0,
                bottom: 20.0,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _formLogin() {
    return Form(
      key: _formKey,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.all(18.0),
            child: Semantics(
              label: translate('$baseTranslate.doc'),
              child: TextFormField(
                enableInteractiveSelection: false,
                controller: tecLogin,
                focusNode: _cpfFocus,
                inputFormatters: [loginFormatter],
                onChanged: (value) {
                  final cpf =
                      value.replaceAll(".", "").replaceAll("-", "").trim();

                  if (cpf.length == 11) {
                    if (TextFieldValidators.cpf(value) == null) {
                      FocusScope.of(context).requestFocus(_senhaFocus);
                    }
                  }
                },
                textInputAction: TextInputAction.next,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                key: Key('tfLogin'),
                keyboardType: TextInputType.number,
                validator: TextFieldValidators.cpf,
                decoration: InputDecoration(
                  prefixIcon: Icon(
                    Icons.person,
                    color: unimedGreen,
                  ),
                  hintText: translate('$baseTranslate.doc'),
                ),
                style: TextStyle(color: unimedGreen),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(18.0),
            child: Semantics(
              label: translate('$baseTranslate.password'),
              child: TextFormField(
                enableInteractiveSelection: false,
                validator: (value) =>
                    TextFieldValidators.password(value, context),
                maxLength: 100,
                obscureText: hidePassword,
                controller: tecPassword,
                focusNode: _senhaFocus,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                textInputAction: TextInputAction.done,
                onFieldSubmitted: (term) => _pressLogin(),
                key: Key('tfPassword'),
                decoration: InputDecoration(
                  prefixIcon: Icon(
                    Icons.vpn_key,
                    color: unimedGreen,
                  ),
                  counterText: '',
                  suffixIcon: IconButton(
                    icon: hidePassword
                        ? Icon(
                            Icons.visibility_off,
                            color: UnimedColors.grayDark,
                          )
                        : Icon(
                            Icons.remove_red_eye,
                            color: unimedGreen,
                          ),
                    onPressed: () async {
                      setState(() {
                        hidePassword = !hidePassword;
                      });
                    },
                  ),
                  hintText: translate('$baseTranslate.password'),
                ),
                style: TextStyle(color: unimedGreen),
              ),
            ),
          ),
          _messageError(),
          Padding(
            padding: const EdgeInsets.only(top: 18.0, right: 18.0, left: 18.0),
            child: ButtonLogin(onPressed: _pressLogin),
          ),
          if (!_isLoading) ...[
            _buttonLostPassword(),
            _virtualCardButton(),
            ButtonSignUp(),
            _quickAccess(),
          ],
        ],
      ),
    );
  }

  void _pressLogin() {
    _mFirebaseAnalytics.logLogin(loginMethod: '_pressLogin');
    FocusScope.of(context).requestFocus(new FocusNode());

    if (_formKey.currentState!.validate()) {
      final _loginCPF = tecLogin.text;
      final _loginPassword = tecPassword.text;

      // tecPassword.clear();
      BlocProvider.of<UserBloc>(context).add(Authenticate(
        credentials: UserCredentials(
          cpf: _loginCPF,
          password: _loginPassword,
        ),
      ));
    }
  }

  Widget _quickAccess() {
    return Padding(
      padding: const EdgeInsets.only(left: 18, right: 18),
      child: SizedBox(
        width: double.infinity,
        child: TextButton(
          style: TextButton.styleFrom(
            backgroundColor: Colors.white,
            foregroundColor: UnimedColors.purple,
            shape: RoundedRectangleBorder(
              borderRadius: new BorderRadius.circular(18.0),
              side: BorderSide(color: UnimedColors.purple),
            ),
          ),
          onPressed:
              _isLoading ? null : () => _openBottomModalQuickAccess(context),
          child: Center(
            child: _isLoading
                ? SpinKitThreeBounce(
                    color: UnimedColors.conectaPurple,
                    size: 20,
                  )
                : Text(
                    translate('$baseTranslate.quickAccess'),
                  ),
          ),
        ),
      ),
    );
  }

  Widget _virtualCardButton() {
    return BlocBuilder<OfflineBloc, OfflineState>(builder: (context, state) {
      return Padding(
          padding: const EdgeInsets.only(left: 18, right: 18),
          child: SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              style: TextButton.styleFrom(
                backgroundColor: UnimedColors.green,
                foregroundColor: UnimedColors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: new BorderRadius.circular(18.0),
                ),
              ),
              onPressed: state is DoneGetPerfilModelOfflineState
                  ? () {
                      //Navigator.pop(context);
                      _openBiometryVirtualCardOffline(state.perfilOfflineModel);
                    }
                  : () => _showAlertNoVirtualCardsInCache(),
              child: Center(
                child: _isLoading
                    ? SpinKitThreeBounce(
                        color: UnimedColors.conectaPurple,
                        size: 20,
                      )
                    : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            '$baseImages/cartao.png',
                            width: 20,
                            color: Colors.white,
                          ),
                          Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 8.0),
                            child: Text(
                              translate('$baseTranslate.virtualCard'),
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ));
    });
  }

  void _openBottomModalQuickAccess(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) {
        return SafeArea(
          child: Container(
            padding: const EdgeInsets.fromLTRB(18.0, 16.0, 18.0, 32.0),
            decoration: BoxDecoration(
                color: UnimedColors.white,
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(90),
                    topRight: Radius.circular(90))),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(
                  height: 20,
                ),
                Center(
                  child: Text(
                    'Acesso rápido',
                    style: TextStyle(
                        fontSize: 16,
                        color: UnimedColors.green,
                        fontWeight: FontWeight.bold),
                  ),
                ),
                const SizedBox(
                  height: 30,
                ),
                ButtonLoginByToten(),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buttonLostPassword() {
    return Semantics(
      label: translate('$baseTranslate.forgot'),
      child: TextButton(
        child: Text(
          translate('$baseTranslate.forgot'),
          style: TextStyle(
            decoration: TextDecoration.underline,
          ),
        ),
        onPressed: () {
          Navigator.push(
              context,
              FadeRouteBuilder(
                  page: BlocProvider(
                create: (context) => RedefinirSenhaBloc(),
                child: RedefinirSenhaScreen(),
              )));
          AnalyticsService().sendEvent(
            name: 'click',
            parameters: {'type': 'link', 'value': 'esqueci_senha'},
          );
        },
      ),
    );
  }

  void _openBiometry() async {
    final credentials = await Locator.instance.get<AuthApi>().getCredentials();
    if (credentials != null &&
        credentials.cpf.isNotEmpty &&
        credentials.password!.isNotEmpty) {
      bool existBiometrics = await localAuth.canCheckBiometrics;
      logger.i('Permitido usar biometria : $existBiometrics');
      try {
        BlocProvider.of<OfflineBloc>(context).add(GetPerfilModelOfflineEvent(
            cpf: credentials.cpf.replaceAll(new RegExp(r'[^0-9]'), '')));
        bool valid = false;
        if (Platform.isAndroid)
          valid = await localAuth.authenticate(
            localizedReason: 'Permitir para acessar',
          );
        else {
          List<BiometricType> availableBiometrics =
              await localAuth.getAvailableBiometrics();

          if (existBiometrics && availableBiometrics.length > 0) {
            if (!_canOpenBiometry) return;

            valid = await localAuth.authenticate(
              localizedReason: 'Permitir para acessar',
            );
          } else {
            logger.i('sem biometria disponível.');
            _loginUser(credentials);
          }
        }

        if (valid) {
          AnalyticsService().sendEvent(name: 'open_biometry', parameters: {
            'type': 'infomation',
            'environment': FlavorConfig.instance!.flavor.toString()
          });

          _loginUser(credentials);
        }
      } on PlatformException catch (e) {
        if (FlavorConfig.isProduction()) {
          if (e.code == 'PlatformException:PermanentlyLockedOut') {
            _showAlertBiometryError(true);
          } else {
            _showAlertBiometryError(false);
          }
        } else {
          _loginUser(credentials);
        }
      } on Exception catch (_) {
        if (FlavorConfig.isProduction()) {
          _showAlertBiometryError(false);
        } else {
          _loginUser(credentials);
        }
      }
    }
    // }
  }

  Future<void> _openBiometryVirtualCardOffline(
      PerfilOfflineModel? perfilOfflineModel) async {
    bool existBiometrics = await localAuth.canCheckBiometrics;
    logger.i('Permitido usar biometria : $existBiometrics');
    try {
      bool valid = false;
      if (Platform.isAndroid)
        valid = await localAuth.authenticate(
          localizedReason: 'Permitir para acessar',
        );
      else {
        List<BiometricType> availableBiometrics =
            await localAuth.getAvailableBiometrics();

        if (existBiometrics && availableBiometrics.length > 0) {
          if (!_canOpenBiometry) return;

          valid = await localAuth.authenticate(
            localizedReason: 'Permitir para acessar',
          );
        } else {
          logger.i('sem biometria disponível.');
          Navigator.push(
              context,
              FadeRouteBuilder(
                  page: CartaoVirtualScreen(
                perfilOfflineModel: perfilOfflineModel,
              )));
        }
      }
      if (valid) {
        Navigator.push(
            context,
            FadeRouteBuilder(
                page: CartaoVirtualScreen(
              perfilOfflineModel: perfilOfflineModel,
            )));
      }
    } on PlatformException catch (e) {
      if (FlavorConfig.isProduction()) {
        if (e.code == 'PlatformException:PermanentlyLockedOut') {
          _showAlertBiometryError(true);
        } else {
          _showAlertBiometryError(false);
        }
      } else {
        Navigator.push(
            context,
            FadeRouteBuilder(
                page: CartaoVirtualScreen(
              perfilOfflineModel: perfilOfflineModel,
            )));
      }
    } on Exception catch (_) {
      if (FlavorConfig.isProduction()) {
        _showAlertBiometryError(false);
      } else {
        Navigator.push(
            context,
            FadeRouteBuilder(
                page: CartaoVirtualScreen(
              perfilOfflineModel: perfilOfflineModel,
            )));
      }
    }
  }

  void _loginUser(UserCredentials credentials) {
    tecLogin.text =
        MaskedTextController(text: credentials.cpf, mask: '000.000.000-00')
            .text;
    tecPassword.text = credentials.password!;
    _pressLogin();
  }

  Widget _messageError() {
    return BlocBuilder<UserBloc, UserState>(builder: (context, state) {
      return BlocBuilder<PerfilBloc, PerfilState>(
          builder: (context, statePerfil) {
        if (state is ErrorUserState) {
          return _errMessage(state.message);
        } else if (statePerfil is ErrorProfilesPermissionsState) {
          return _errMessage(statePerfil.message);
        } else {
          return Container();
        }
      });
    });
  }

  Widget _errMessage(String message) {
    return Padding(
      key: Key('message_error_login'),
      padding: EdgeInsets.all(10),
      child: Text(
        '   $message   ',
        textAlign: TextAlign.center,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          backgroundColor: Colors.red,
          color: Colors.white,
        ),
      ),
    );
  }

  _showAlertNoVirtualCardsInCache() {
    Alert.open(context,
        title: 'Atenção',
        text: 'Para acessar o cartão virtual, é preciso fazer login antes.',
        callbackClose: () =>
            Navigator.popUntil(context, (route) => route.isFirst));
  }

  _showAlertBiometryError(bool isPermanentlyLockedOut) {
    Alert.open(context,
        title: 'Atenção',
        text: isPermanentlyLockedOut
            ? 'A biometria do aparelho foi bloqueada por excesso de tentativas, tente novamente mais tarde.'
            : 'Ocorreu um erro ao iniciar a biometria. Tente novamente mais tarde.',
        callbackClose: () =>
            Navigator.popUntil(context, (route) => route.isFirst));
  }
}
