import 'package:cliente_minha_unimed/bloc/clube-mais-vantagens/club-mais-vantagens_event.dart';
import 'package:cliente_minha_unimed/bloc/clube-mais-vantagens/club-mais-vantagens_state.dart';
import 'package:cliente_minha_unimed/shared/api/clube-mais-vantagens.graphql.dart'; 
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ClubMaisVantagensBloc
    extends Bloc<ClubMaisVantagensEvent, ClubMaisVantagensState> {
  ClubMaisVantagensBloc() : super(InitialClubMaisVantagensState());
  final logger = UnimedLogger(className: 'ClubMaisVantagens');
 @override
  Stream<ClubMaisVantagensState> mapEventToState(ClubMaisVantagensEvent event) async* {
    if (event is GetClubMaisVantagensEvent) {
      yield* _mapGetClubMaisVantagensToState(event);
    } else if (event is CadastrarClubMaisVantagensEvent) {
      yield* _mapCadastrarClubMaisVantagensToState(event);
    }
  }

  Stream<ClubMaisVantagensState> _mapGetClubMaisVantagensToState(GetClubMaisVantagensEvent event) async* {
    yield LoadingClubMaisVantagensState();
    try {
      final clubMaisVantagens = await Locator.instance<ClubeMaisVantagensApi>()
            .verificaCadastroClubMaisVantagens(token: event.token);
      yield LoadedClubMaisVantagensState(clubMaisVantagens: clubMaisVantagens);
    } catch (e) {
      yield ErrorClubMaisVantagensState(message: e.toString());
    }
  }

  Stream<ClubMaisVantagensState> _mapCadastrarClubMaisVantagensToState(CadastrarClubMaisVantagensEvent event) async* {
    yield LoadingClubMaisVantagensState();
    try {
      final response = await Locator.instance<ClubeMaisVantagensApi>().cadastrarClubeMaisVantagens(token: event.token);
      yield SucessClubMaisVantagensState(message: response.message.toString());
    } catch (e) {
      yield ErrorClubMaisVantagensState(message: e.toString());
    }
  }

  
}
