import 'dart:convert';

import 'package:cliente_minha_unimed/models/fatura-lista.model.dart';
import 'package:cliente_minha_unimed/shared/api/graphql.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';
import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:graphql/client.dart';
import 'package:http_client/http_client.dart';

class FinanceiroGraphQlApi extends GraphQlApi {
    FinanceiroGraphQlApi(UnimedHttpClient httpClient) : super(httpClient);

    final UnimedLogger logger = UnimedLogger(className: 'FinanceiroGraphQlApi');

      Future<Map<String, dynamic>> getPixCopiaCola({required String card, required String invoiceDate}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
        query GetPixCopyAndPaste {
            getPixCopyAndPaste(cardId: "$card", invoiceDate: "$invoiceDate") {
                success
                data {
                    qrCodeText
                    invoiceNumber
                }
            }
        } 
      ''';

      logger.d('getPixCopiaCola query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('getPixCopiaCola exception: ${result.exception}');
        throw UnimedException(MessageException.GENERAL);
      } else {
        var info = result.data!['getPixCopyAndPaste']['data'];

        return info;
      }
    } on UnimedException catch (ex) {
      logger.e('getPixCopiaCola ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('getPixCopiaCola exception : $ex');
      throw GraphQlException(MessageException.GENERAL);
    }
  }

  Future<String> getPixQrCode({required String card, required String invoiceDate}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
      query GetPixQRCode {
          getPixQRCode(cardId: "$card", invoiceDate: "$invoiceDate") {
              success
              data
          }
      }
      ''';

      logger.d('GetPixQRCode query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('GetPixQRCode exception: ${result.exception}');
        throw UnimedException(MessageException.GENERAL);
      } else {
        final bool success = result.data!['getPixQRCode']['success'] as bool;

        if (success) {
          final String qrCodeBase64 = result.data!['getPixQRCode']['data'] as String;
          return qrCodeBase64;
        } else {
          throw UnimedException('Falha ao gerar QR Code');
        }
      }
    } on UnimedException catch (ex) {
      logger.e('GetPixQRCode ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('GetPixQRCode exception : $ex');
      throw GraphQlException(MessageException.GENERAL);
    }
  }

  Future<List<FaturaLista>> getLast12Invoices({required String card}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
      query GetLast12Invoices {
          getLast12Invoices(card: "$card") {
              dateDue
              value
              updatedValue
              situation
              havePixCode
          }
      }
      ''';

      logger.d('getLast12Invoices query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('getLast12Invoices exception: ${result.exception}');
        throw UnimedException(MessageException.GENERAL);
      } else {
        final List<dynamic> data = result.data!['getLast12Invoices'] as List;
        print(data.first);
        print(jsonEncode(data.first));
        logger.d('getLast12Invoices success list ${data.length}');

        List<FaturaLista> _list = data.map((f) {
          final fatura = FaturaLista.fromEnglishJson(f);

          return fatura;
        }).toList();

        return _list;
      }
    } on UnimedException catch (ex) {
      logger.e('getLast12Invoices ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('getLast12Invoices exception : $ex');
      throw GraphQlException(MessageException.GENERAL);
    }
  }
}