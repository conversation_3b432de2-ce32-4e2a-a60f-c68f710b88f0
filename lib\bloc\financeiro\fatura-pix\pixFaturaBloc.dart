import 'package:cliente_minha_unimed/bloc/financeiro/fatura-pix/pixFaturaEvent.dart';
import 'package:cliente_minha_unimed/bloc/financeiro/fatura-pix/pixFaturaState.dart';
import 'package:cliente_minha_unimed/shared/api/financeiro.graphql.dart';
import 'package:cliente_minha_unimed/shared/locator.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';

class PixFaturaBloc extends Bloc<PixFaturaEvent, PixFaturaState> {


  PixFaturaBloc() : super(InitialPixFaturaState());

  @override
  Stream<PixFaturaState> mapEventToState(PixFaturaEvent event) async* {
    if (event is GeneratePixCode) {
      yield LoadingPixFaturaState(fatura: event.fatura);

     
      try {
        var qrcodeResult = await Locator.instance<FinanceiroGraphQlApi>().getPixQrCode(
          card: event.perfil.contratoBeneficiario.carteira!.carteiraNumero,
          invoiceDate: formatInvoiceDate(event.invoiceDate),
        );

        var copyPastResult = await Locator.instance<FinanceiroGraphQlApi>().getPixCopiaCola(
          card: event.perfil.contratoBeneficiario.carteira!.carteiraNumero,
          invoiceDate: formatInvoiceDate(event.invoiceDate),
        );

        if (copyPastResult['qrCodeText'] != null) {
         
          yield DonePixFaturaState(
            fatura: event.fatura,
            pixCopiaCola: copyPastResult['qrCodeText'],
            pixQrCode: qrcodeResult,
          );
        } else {
          yield ErrorPixFaturaState(
            fatura: event.fatura,
            message: 'Erro ao gerar o código Pix',
          );
        }
      } catch (e) {
        yield ErrorPixFaturaState(
          fatura: event.fatura,
          message: e.toString(),
        );
      }
    }
  }

  String formatInvoiceDate(String originalDate) {
    try {
      List<String> parts = originalDate.split('/');

      if (parts.length != 3) {
        throw FormatException('Formato de data inválido');
      }

      String month = parts[1];
      String year = parts[2];

      month = month.replaceFirst(RegExp(r'^0+'), '');

      return '$month-$year';
    } catch (e) {
      print('Erro ao converter a data: $e');
      return originalDate;
    }
  }
}
