import 'dart:async';
import 'dart:convert';

import 'package:cliente_minha_unimed/models/res/allergy.model.dart';
import 'package:cliente_minha_unimed/models/res/diagnostic/detail/diagnostic_details.model.dart';
import 'package:cliente_minha_unimed/models/res/diagnostic/res_diagnostic.model.dart';
import 'package:cliente_minha_unimed/models/res/documents.model.dart';
import 'package:cliente_minha_unimed/models/res/exam/exam_model.dart';
import 'package:cliente_minha_unimed/models/res/exam/res_exam_result_image_detail_model.dart';
import 'package:cliente_minha_unimed/models/res/exam/res_exam_result_laboratory_detail_model.dart';
import 'package:cliente_minha_unimed/models/res/procedures/res_attendence_model.dart';
import 'package:cliente_minha_unimed/models/res/res_alert_model.dart';
import 'package:cliente_minha_unimed/models/res/res_brazil_attendance.model.dart';
import 'package:cliente_minha_unimed/models/res/res_configs_model.dart';
import 'package:cliente_minha_unimed/models/res/res_documents.model.dart';
import 'package:cliente_minha_unimed/models/res/res_exam.model.dart';
import 'package:cliente_minha_unimed/models/res/res_image_exam.model.dart'
    as attendances;
import 'package:cliente_minha_unimed/models/res/res_indicator_data.model.dart';
import 'package:cliente_minha_unimed/models/res/res_indicator_model.dart';
import 'package:cliente_minha_unimed/models/res/res_phisical_exam.model.dart';
import 'package:cliente_minha_unimed/shared/api/graphql.api.dart';
import 'package:cliente_minha_unimed/shared/exceptions.dart';

import 'package:cliente_minha_unimed/shared/messages.exceptions.dart';
import 'package:cliente_minha_unimed/shared/utils/logger_print.dart';
import 'package:flutter/material.dart';
import 'package:graphql/client.dart';
import 'package:http_client/http_client.dart';
import 'package:intl/intl.dart';

class ResApi extends GraphQlApi {
  ResApi(UnimedHttpClient httpClient) : super(httpClient);

  final logger = UnimedLogger(className: 'ResApi');

  Future<List<ResAllergyModel>> resListAllergies({
    required String cpf,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
          query ResBrazilBeneficiaryAllergies {
            resBrazilBeneficiaryAllergies(cpf: "$cpf") {
              alergeno
              local
              data
              categoria
              timestamp
            }
          }
      ''';

      logger.e('resListAllergies query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );
      final QueryResult result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e('resListAllergies exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final data = result.data!['resBrazilBeneficiaryAllergies'] as List;
        logger.d('resListAllergies success list ${data.length}');

        final collection = data.map((e) {
          return ResAllergyModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException saveContact -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('resListAllergies ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('resListAllergies exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<List<ResAlertModel>> resGetAllerts({
    required String cpf,
    DateTimeRange? dateTimeRange,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String date = "";
      if (dateTimeRange != null) {
        date = '''
          startDate: "${DateFormat("yyyy-MM-dd").format(dateTimeRange.start)}"
          endDate:  "${DateFormat("yyyy-MM-dd").format(dateTimeRange.end)}"
        ''';
      }
      String query = '''
        query ResBrazilBeneficiaryAlerts {
            resBrazilBeneficiaryAlerts(
            cpf: "$cpf"
            $date
            ) {
              code
              codeOrigin
              origin
              alerts
              status
              comments
              originType
              timestamp
              date
            }
        }
      ''';

      logger.d('resGetAllerts query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('resGetAllerts exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final List<dynamic> data =
            result.data!['resBrazilBeneficiaryAlerts'] as List;
        logger.d('resGetAllerts success list ${data.length}');

        final List<ResAlertModel> collection = data.map((e) {
          return ResAlertModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('resGetAllerts ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('resGetAllerts exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<List<ResBrazilAttendanceModel>> resGetAttendances({
    required String cpf,
    DateTimeRange? dateTimeRange,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String date = "";
      if (dateTimeRange != null) {
        date = '''
          startDate: "${DateFormat("yyyy-MM-dd").format(dateTimeRange.start)}"
          endDate:  "${DateFormat("yyyy-MM-dd").format(dateTimeRange.end)}"
        ''';
      }

      String query = '''
          query ResBrazilBeneficiaryAttendances {
            resBrazilBeneficiaryAttendances(
                cpf: "$cpf"
                $date
              ) {
              code
              type
              description
              date
              dataId
              filters {
                description
              }
            }
          }
      ''';

      logger.i('resGetAttendances query : $query');

      final options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e('resGetAttendances exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final data = result.data!['resBrazilBeneficiaryAttendances'] as List;
        logger.d('resGetAttendances success list ${data.length}');

        final collection = data.map((e) {
          return ResBrazilAttendanceModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException resGetAttendances -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('resGetAttendances ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('resGetAttendances exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<DiagnosticDetailsModel> resGetDiagnosticDetails({
    required String cpf,
    required String dataId,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
          query ResBrazilBeneficiaryAttendances {
            resBrazilBeneficiaryAttendanceDetails(
                cpf: "$cpf"
                dataId: "$dataId"
              ) {
              name
              diagnostic {
                doctor
                date
                diagnosticName {
                  description
                }
              }
            }
          }
      ''';

      logger.i('resGetDiagnosticDetails query : $query');

      final options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e('resGetDiagnosticDetails exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final data = result.data!['resBrazilBeneficiaryAttendanceDetails'];
        logger.d('resGetDiagnosticDetails success');

        return DiagnosticDetailsModel.fromJson(data);
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException resGetDiagnosticDetails -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('resGetDiagnosticDetails ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('resGetDiagnosticDetails exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<List<ResBrazilDiagnosticModel>> resGetDiagnostics({
    required String card,
    DateTimeRange? dateTimeRange,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String date = "";
      if (dateTimeRange != null) {
        date = '''
          startDate: "${DateFormat("yyyy-MM-dd").format(dateTimeRange.start)}"
          endDate:  "${DateFormat("yyyy-MM-dd").format(dateTimeRange.end)}"
        ''';
      }

      String query = '''
        query ResBrazilBeneficiaryDiagnosticByCard {
            resBrazilBeneficiaryDiagnosticByCard(
            card: "$card"
            $date
            ) {
              admissionDate
              dischargeDate
              codeAttendanceEncode
              code
              typeDiagnostic
              localName
              itemsAttendance{
                description
              }
            }
        }
      ''';

      logger.i('resGetDiagnosticDetails query : $query');

      final options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e(
            'resBrazilBeneficiaryDiagnosticByCard exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final data =
            result.data!['resBrazilBeneficiaryDiagnosticByCard'] as List;
        logger.d('resBrazilBeneficiaryDiagnosticByCard success');

        final collection = data.map((e) {
          return ResBrazilDiagnosticModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException resBrazilBeneficiaryDiagnosticByCard -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('resBrazilBeneficiaryDiagnosticByCard ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('resBrazilBeneficiaryDiagnosticByCard exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<attendances.ResImageExamModel> resGetImageExamDetails({
    required String cpf,
    required String dataId,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
          query ResBrazilBeneficiaryAttendances {
            resBrazilBeneficiaryAttendanceDetails(
                cpf: "$cpf"
                dataId: "$dataId"
              ) {
              name
              imageExams {
                requester
                procedures {
                  description
                }
              }
            }
          }
      ''';

      logger.i('resGetImageExamDetails query : $query');

      final options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e('resGetImageExamDetails exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final data = result.data!['resBrazilBeneficiaryAttendanceDetails'];
        logger.d('resGetImageExamDetails success');

        return attendances.ResImageExamModel.fromJson(data);
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException resGetImageExamDetails -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('resGetImageExamDetails ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('resGetImageExamDetails exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<ResExamModel> resGetExamDetails({
    required String cpf,
    required String dataId,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
          query ResBrazilBeneficiaryAttendances {
            resBrazilBeneficiaryAttendanceDetails(
                cpf: "$cpf"
                dataId: "$dataId"
              ) {
              name
              exams {
                requester
                procedures {
                  description
                }
              }
            }
          }
      ''';

      logger.i('resGetExamDetails query : $query');

      final options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e('resGetExamDetails exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final data = result.data!['resBrazilBeneficiaryAttendanceDetails'];
        logger.d('resGetExamDetails success');

        return ResExamModel.fromJson(data);
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException resGetExamDetails -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('resGetExamDetails ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('resGetExamDetails exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<ResDocumentsModel> resGetDocumentDetails({
    required String cpf,
    required String dataId,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
          query ResBrazilBeneficiaryAttendances {
            resBrazilBeneficiaryAttendanceDetails(
                cpf: "$cpf"
                dataId: "$dataId"
              ) {
              name
              documents {
                doctor
                content {
                    description
                    streamDescription
                    documentType
                    fileType
                    documentURL
                    timestamp
                    date
                }
              }
            }
          }
      ''';

      logger.i('resGetDocumentsDetails query : $query');

      final options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e('resGetDocumentsDetails exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final data = result.data!['resBrazilBeneficiaryAttendanceDetails'];
        logger.d('resGetDocumentsDetails success');

        return ResDocumentsModel.fromJson(data);
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException resGetDocumentsDetails -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('resGetDocumentsDetails ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('resGetDocumentsDetails exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<ResPhisicalExamModel> resGetPhisicalExamDetails({
    required String cpf,
    required String dataId,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
          query ResBrazilBeneficiaryAttendances {
            resBrazilBeneficiaryAttendanceDetails(
                cpf: "$cpf"
                dataId: "$dataId"
              ) {
              name              
              phisicalExam {
                indicator
                unit
                value
              }
            }
          }
      ''';

      logger.i('resGetPhisicalExamDetails query : $query');

      final options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e('resGetPhisicalExamDetails exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final data = result.data!['resBrazilBeneficiaryAttendanceDetails'];
        logger.d('resGetPhisicalExamDetails success');

        return ResPhisicalExamModel.fromJson(data);
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException resGetPhisicalExamDetails -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('resGetPhisicalExamDetails ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('resGetPhisicalExamDetails exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<List<IndicatorModel>> resListIndicators({
    required String cpf,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();
      String query = '''
        query ResBrazilBeneficiaryIndicators {
          resBrazilBeneficiaryIndicators(cpf: "$cpf") {
            id
            description
            selected
           }
        }
      ''';

      logger.d('resListIndicators query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e('resListIndicators exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final data = result.data!['resBrazilBeneficiaryIndicators'] as List;
        logger.d('resListIndicators success list ${data.length}');

        final collection = data.map((e) {
          return IndicatorModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException saveContact -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('resListIndicators ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('resListIndicators exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<List<IndicatorDataModel>> resIndicatorsData({
    required String cpf,
    required List<String> indicatorsId,
    required DateTimeRange dateRange,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();
      String query = '''
       query ResBrazilBeneficiaryIndicators {
          resBrazilBeneficiaryIndicatorsData(
              cpf: "$cpf"
              indicatorIds: "${indicatorsId.join('-')}"
              startDate: "${DateFormat("yyyy-MM-dd").format(dateRange.start)}"
              endDate: "${DateFormat("yyyy-MM-dd").format(dateRange.end)}"
          ) {
              id
              unity
              indicatorsData {
                  date
                  x
                  y
              }
          }
        }
      ''';

      logger.d('resIndicatorsData query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e('resIndicatorsData exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final data = result.data!['resBrazilBeneficiaryIndicatorsData'] as List;
        logger.d('resIndicatorsData success list ${data.length}');

        final collection = data.map((e) {
          return IndicatorDataModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException saveContact -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('resListIndicators ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('resListIndicators exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<ResConfigModel> resConfigs(
      {required String card, required String cpf}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
       query ConfigAppClienteResBrazil {
            configAppClienteResBrazil(card: "$card") {
                allergies {
                    categories {
                        code
                        description
                    }
                }
                popup {
                    enable
                    checkAccept
                    title
                    subtitle
                }
            }

            resBrazilBeneficiaryIndicatorList(cpf: "$cpf") {
                id
                description
            }
        }
      ''';

      logger.d('ConfigAppClienteResBrazil query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('configAppClienteResBrazil exception : ${result.exception}');
        throw UnimedException(MessageException.GENERAL);
      } else {
        logger.d(
            'ConfigAppClienteResBrazil query : ${result.data!['configAppClienteResBrazil']}');
        logger.d(
            'ConfigAppClienteResBrazil query : ${result.data!['resBrazilBeneficiaryIndicatorList']}');

        final Map<String, dynamic> data =
            (result.data!['configAppClienteResBrazil'] as Map<String, dynamic>);
        final dataIndicators =
            result.data!['resBrazilBeneficiaryIndicatorList'] as List;

        data['indicators'] = dataIndicators;
        final ResConfigModel resConfigModel = ResConfigModel.fromJson(data);

        return resConfigModel;
      }
    } on UnimedException catch (ex) {
      logger.e('ConfigAppClienteResBrazil ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('ConfigAppClienteResBrazil exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<ResConfigModel> resConfigsPopup(
      {required String card, required String cpf}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
       query ConfigAppClienteResBrazil {
            configAppClienteResBrazil(card: "$card") {           
                popup {
                    enable
                    checkAccept
                    title
                    subtitle
                }
            }
        }
      ''';

      logger.d('ConfigAppClienteResBrazil query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('configAppClienteResBrazil exception : ${result.exception}');
        throw UnimedException(MessageException.GENERAL);
      } else {
        logger.d(
            'ConfigAppClienteResBrazil query : ${result.data!['configAppClienteResBrazil']}');

        final Map<String, dynamic> data =
            (result.data!['configAppClienteResBrazil'] as Map<String, dynamic>);

        ResConfigModel resConfigModel = ResConfigModel(
            allergies: Allergies(categories: []),
            indicators: [],
            popup: ResPopup.fromJson(data['popup'] as Map<String, dynamic>));

        return resConfigModel;
      }
    } on UnimedException catch (ex) {
      logger.e('ConfigAppClienteResBrazil ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('ConfigAppClienteResBrazil exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<List<dynamic>> fetchExams({
    required String cpf,
    DateTimeRange? dateTimeRange,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String date = "";
      if (dateTimeRange != null) {
        date = '''
          startDate: "${DateFormat("yyyy-MM-dd").format(dateTimeRange.start)}"
          endDate:  "${DateFormat("yyyy-MM-dd").format(dateTimeRange.end)}"
        ''';
      }

      String query = '''
      query ResBrazilBeneficiaryExams {
        resBrazilBeneficiaryImageExams(cpf: "$cpf" $date) {
          code
          description
          date
          timestamp
          dataId
        }
        resBrazilBeneficiaryLaboratoryExam(cpf: "$cpf"  $date) {
          code
          description
          date
        }
      }
    ''';

      logger.e('fetchExams query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(query),
      );

      final QueryResult result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e('fetchExams exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final imageExamsData =
            result.data!['resBrazilBeneficiaryImageExams'] as List;
        final labExamsData =
            result.data!['resBrazilBeneficiaryLaboratoryExam'] as List;

        logger.d('fetchExams success imageExams list ${imageExamsData.length}');
        logger.d('fetchExams success labExams list ${labExamsData.length}');

        final imageExams = imageExamsData.map((e) {
          return ResImageExamModel.fromJson(e);
        }).toList();

        final labExams = labExamsData.map((e) {
          return ResLaboratoryExamModel.fromJson(e);
        }).toList();

        final combinedExams = [...imageExams, ...labExams];

        return combinedExams;
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException fetchExams -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('fetchExams ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('fetchExams exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<ResExamResultLaboratoryDetailModel> searchForLaboratoryTestDetails({
    required String cpf,
    required String code,
    required String description,
    required String dateExam,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
      query ResBrazilBeneficiaryDetailsLaboratoryExam {
        resBrazilBeneficiaryDetailsLaboratoryExam(
            cpf: "$cpf"
            code: "$code"
            description: "$description"
            dateExam: "$dateExam"
        ) {
            exams {
                requester
                procedures {
                    description
                }
            }
        }
    }
    ''';

      logger.e('fetchExams query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(query),
      );

      final QueryResult result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e(
            'searchForLaboratoryTestDetails exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final Map<String, dynamic> resultJson =
            result.data!['resBrazilBeneficiaryDetailsLaboratoryExam']
                as Map<String, dynamic>;

        final ResExamResultLaboratoryDetailModel examDetails =
            ResExamResultLaboratoryDetailModel.fromJson(resultJson);

        return examDetails;
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException searchForLaboratoryTestDetails -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('searchForLaboratoryTestDetails ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('searchForLaboratoryTestDetails exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<ResExamResultImageDetailModel> searchForImageTestDetails({
    required String cpf,
    required String dataId,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
   

    query ResBrazilBeneficiaryDetailsImageExam {
    resBrazilBeneficiaryDetailsImageExam(
        cpf: "$cpf"
        dataId: "$dataId"
    ) {
        exams {
            requester
            procedures {
                code
                documentUrl
                nomeProcedimento
                nomeMedico
            }
        }
    }
}

    ''';

      logger.e('searchForImageTestDetails fetchExams query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(query),
      );

      final QueryResult result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e('searchForImageTestDetails exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message);
      } else {
        final Map<String, dynamic> resultJson =
            result.data!["resBrazilBeneficiaryDetailsImageExam"]
                as Map<String, dynamic>;

        final ResExamResultImageDetailModel examImageDetails =
            ResExamResultImageDetailModel.fromJson(resultJson);

        return examImageDetails;
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException searchForImageTestDetails -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('searchForImageTestDetails ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('searchForImageTestDetails exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<String> ResPdfConsent({required String cpf}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
          query ResBrazilBeneficiaryConsentTermPDF {
            resBrazilBeneficiaryConsentTermPDF(cpf: "$cpf") {
              base64PDF
            }
          }
      ''';

      logger.e('ResPdfConsent query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e('ResPdfConsent exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final data = result.data!['resBrazilBeneficiaryConsentTermPDF']
            as Map<String, dynamic>;
        final base64PDF = data['base64PDF'] as String;
        logger.d('ResPdfConsent success: $base64PDF');

        return base64PDF;
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException saveContact -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('ResPdfConsent ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('ResPdfConsent exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<String> ResConsetTermsMutation(
      {required String cpf, required bool value}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String mutation = '''
          mutation ResBrazilBeneficiaryConsentTerm {
            resBrazilBeneficiaryConsentTerm(cpf: "$cpf", accepted: $value) {
              message
              date
            }
          }
      ''';

      logger.e('resBrazilBeneficiaryConsentTerm query : $mutation');

      final QueryOptions options = QueryOptions(
        document: gql(
          mutation,
        ),
      );

      final QueryResult result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e(
            'resBrazilBeneficiaryConsentTerm exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final data = result.data!['resBrazilBeneficiaryConsentTerm']
            as Map<String, dynamic>;
        final accepted = data['message'] as String;
        logger.d('ResPdfConsent success: $accepted');

        return accepted;
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException saveContact -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('resBrazilBeneficiaryConsentTerm ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('resBrazilBeneficiaryConsentTerm exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<bool> ResConsetTermsVerifyAccept({required String cpf}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String query = '''
          query ResBrazilBeneficiaryCheckConsentTerm {
            resBrazilBeneficiaryCheckConsentTerm(cpf: "$cpf") {
              accepted
              date
            }
          }
      ''';

      logger.e('ResBrazilBeneficiaryCheckConsentTerm query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e(
            'ResBrazilBeneficiaryCheckConsentTerm exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final data = result.data!['resBrazilBeneficiaryCheckConsentTerm']
            as Map<String, dynamic>;
        final accepted = data['accepted'] as bool;
        logger.d('ResBrazilBeneficiaryCheckConsentTerm success: $accepted');

        return accepted;
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException saveContact -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('ResBrazilBeneficiaryCheckConsentTerm ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('ResPdfConsent exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

  Future<List<ResBrazilDocumentsModel>> resGetDocuments({
    required String card,
    DateTimeRange? dateTimeRange,
  }) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String date = "";
      if (dateTimeRange != null) {
        date = '''
          startDate: "${DateFormat("yyyy-MM-dd").format(dateTimeRange.start)}"
          endDate:  "${DateFormat("yyyy-MM-dd").format(dateTimeRange.end)}"
        ''';
      }

      String query = '''
        query ResBrazilBeneficiaryDocumentsByCard {
            resBrazilBeneficiaryDocumentsByCard(
            card: "$card"
            $date
            ) {
              code
              typeDocument
              localName
              admissionDate
              dischargeDate
              codeAttendanceEncode
              itemsAttendance{
                description
              }
            }
        }
      ''';

      logger.i('resBrazilBeneficiaryDocumentsByCard query : $query');

      final options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final result =
          await client.query(options).timeout(const Duration(seconds: 60));

      if (result.hasException) {
        logger.e(
            'resBrazilBeneficiaryDocumentsByCard exception : ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ??
            MessageException.GENERAL;
        throw UnimedException(message + ' (-1000)');
      } else {
        final data =
            result.data!['resBrazilBeneficiaryDocumentsByCard'] as List;
        logger.d('resBrazilBeneficiaryDocumentsByCard success');

        final collection = data.map((e) {
          return ResBrazilDocumentsModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on TimeoutException catch (e) {
      logger.e('TimeoutException resBrazilBeneficiaryDocumentsByCard -  $e');
      throw UnimedException(MessageException.GENERIC_TIMEOUT);
    } on UnimedException catch (ex) {
      logger.e('resBrazilBeneficiaryDocumentsByCard ${ex.runtimeType} : $ex');
      throw UnimedException(ex.message);
    } catch (ex) {
      logger.e('resBrazilBeneficiaryDocumentsByCard exception : $ex');
      throw GraphQlException(ex.toString());
    }
  }

   Future<List<ResAttendanceModel>> resGetProcedureClient({required String card, DateTimeRange? dataRange}) async {
    try {
      final GraphQLClient client = await getGithubGraphQLClient();

      String dateStart = "";
      String dateEnd = "";
      if (dataRange != null) {
        dateStart = dataRange.start.toIso8601String();
        dateEnd = dataRange.end.toIso8601String();
      }

      String query = '''
      query ResBrazilBeneficiaryProcedureByCard {
    resBrazilBeneficiaryProcedureByCard(card: "$card", startDate: "$dateStart", endDate: "$dateEnd") {
        code
        typeProcedure
        localName
        admissionDate
        codeAttendanceEncode
        codigo
        tipo
        nomeLocal
        dataEntrada
        codigoAtendimentoEncode
        itemsAttendance {
            description
        }
        itensAtendimento {
            descricao
        }
        dataAlta
    }
}

      ''';

      logger.d('resGetAttendanceByType query : $query');

      final QueryOptions options = QueryOptions(
        document: gql(
          query,
        ),
      );

      final QueryResult result = await client.query(options);

      if (result.hasException) {
        logger.e('resGetProcedureClient exception: ${result.exception}');
        final message = result.exception?.graphqlErrors.firstOrNull?.message ?? MessageException.GENERAL;
        throw UnimedException(message + " (-1000)");
      } else {
        final List<dynamic> data = result.data!['resBrazilBeneficiaryProcedureByCard'] as List;
        print(data);
        logger.d('resGetProcedureClient success list ${data.length}');
        print(jsonEncode(data));

        final List<ResAttendanceModel> collection = data.map((e) {
          return ResAttendanceModel.fromJson(e);
        }).toList();

        return collection;
      }
    } on UnimedException catch (ex) {
      logger.e('resGetAttendanceByType ${ex.runtimeType} : $ex');
      throw GraphQlException(ex.message);
    } catch (ex) {
      logger.e('resGetAttendanceByType exception : $ex');
      throw GraphQlException(MessageException.GENERAL);
    }
  }
}
